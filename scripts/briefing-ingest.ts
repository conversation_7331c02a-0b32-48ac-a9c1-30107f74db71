#!/usr/bin/env bun
import { ensureDb, initDb, initializeStatements, upsertItem, startRun, finishRun } from "../src/briefing/db.ts";
import { loadAllSources } from "../src/briefing/sources/index.ts";
import { ItemSchema } from "../src/briefing/types.ts";

async function main() {
  const db = ensureDb();
  initDb(db);
  initializeStatements(db);

  let runId = -1; // Initialize with a non-valid ID
  let fetched = 0;
  let validated = 0;
  let inserted = 0;
  let validationFailed = 0;
  let upsertFailed = 0;

  try {
    runId = startRun(db);
    const items = await loadAllSources();
    fetched = items.length;

    for (const it of items) {
      const parsed = ItemSchema.safeParse(it);
      if (!parsed.success) {
        console.warn('Item schema validation failed for item:', it.id || it.url || it.title, parsed.error.issues);
        validationFailed++;
        continue;
      }
      validated++;

      try {
        const { inserted: itemInserted, updated: itemUpdated } = upsertItem(db, parsed.data);
        if (itemInserted) {
          inserted++;
        } else if (itemUpdated) {
          // Optionally track updated items if needed, for now we just count inserted
        }
      } catch (ue) {
        console.warn('upsertItem failed for', parsed.data.id, ue);
        upsertFailed++;
      }
    }

    console.log(`Ingested: fetched=${fetched}, validated=${validated}, inserted=${inserted}, validationFailed=${validationFailed}, upsertFailed=${upsertFailed}`);
  } catch (e) {
    console.error("Ingest error", e);
    process.exitCode = 1;
  } finally {
    if (runId !== -1) {
      finishRun(db, runId, { fetched, validated, inserted, validationFailed, upsertFailed });
    } else {
      // If runId was never set, ensure a default finishRun is called
      finishRun(db, -1, { fetched, validated, inserted, validationFailed, upsertFailed });
    }
  }
}
main();
