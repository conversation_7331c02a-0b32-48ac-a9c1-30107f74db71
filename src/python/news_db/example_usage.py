"""Example usage of the ArticleDB with deduplication.

Run:
    python src/python/news_db/example_usage.py
"""

import sys
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = BASE_DIR.parent
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

from news_db import ArticleDB

DB_PATH = BASE_DIR / "articles.db"


def main() -> None:
    BASE_DIR.mkdir(parents=True, exist_ok=True)
    with ArticleDB(str(DB_PATH)) as db:
        samples = [
            {
                "url": "https://example.com/news/1",
                "title": "Breaking: Something happened",
                "content": "Hello world! This is content #1",
            },
            {
                # Duplicate by URL
                "url": "https://example.com/news/1",
                "title": "Breaking: Something happened (duplicate)",
                "content": "Some different text but same URL",
            },
            {
                # Duplicate by content hash
                "url": "https://example.com/news/2",
                "title": "Another item",
                "content": "Hello world! This is content #1",  # same content as first
            },
        ]

        for s in samples:
            result = db.store_article(url=s["url"], title=s["title"], content=s["content"]) 
            status = result["status"]
            art = result["article"]
            if status == "inserted":
                print(f"Inserted id={art.id} url={art.url} hash={art.content_hash}")
            else:
                print(f"Duplicate by {result['reason']}: id={art.id} url={art.url}")

        print("\nRecent articles:")
        for art in db.get_recent_articles(limit=10):
            print(f"- [{art.fetched_date}] {art.title} <{art.url}> (hash={art.content_hash[:12]}…)")


if __name__ == "__main__":
    main()
