import { StreamStepAggregation } from "@/types/orchestrator-types";
import { extractToolCallId, safeJsonParse } from "./formatting";

export const finalizeStreamAggregator = (agg: StreamStepAggregation): { result: any; meta: { writeConfirmed: boolean; errors: string[] } } => {
  const text = agg.textChunks.join('');
  const result: any = {};
  if (text.trim().length > 0) {
    result.text = text;
  }
  if (agg.toolCalls.length > 0) {
    result.toolCalls = agg.toolCalls.map(call => ({
      toolName: call.name,
      args: call.args,
      result: call.result,
      toolCallId: call.id,
    }));
  }
  if (agg.structuredAnswer) {
    result.structuredCodegen = agg.structuredAnswer;
  }
  return {
    result,
    meta: {
      writeConfirmed: agg.writeConfirmed,
      errors: agg.errors,
    },
  };
};

export const normalizeStreamCompletion = (stream: any): Promise<void> | undefined => {
  try {
    const completed = stream?.completed;
    if (completed && typeof completed.then === 'function') {
      return completed as Promise<void>;
    }
  } catch {
    /* ignore completion inspection errors */
  }
  return undefined;
};

export const observeStreamEventForAggregation = (agg: StreamStepAggregation, event: any): void => {
    if (!agg || event == null) return;

    const recordToolResult = (id: string | undefined, name: string, raw: any) => {
      const normalizedResult = raw;
      let matching = id
        ? [...agg.toolCalls].reverse().find(call => call.id === id)
        : undefined;

      if (!matching) {
        matching = [...agg.toolCalls].reverse().find(call => call.name === name && typeof call.result === 'undefined');
      }

      if (matching) {
        matching.result = normalizedResult;
        if (id && !matching.id) matching.id = id;
      } else {
        agg.toolCalls.push({ id, name, result: normalizedResult });
      }

      if (!agg.structuredAnswer && typeof name === 'string' && name.toLowerCase() === 'answer' && normalizedResult) {
        if (typeof normalizedResult === 'object') {
          agg.structuredAnswer = normalizedResult;
        } else if (typeof normalizedResult === 'string') {
          const parsed = safeJsonParse(normalizedResult);
          if (parsed && typeof parsed === 'object') {
            agg.structuredAnswer = parsed;
          }
        }
      }

      if (typeof name === 'string' && name === 'file_edit' && normalizedResult && typeof normalizedResult === 'object') {
        const success = typeof normalizedResult.success === 'boolean' ? normalizedResult.success : true;
        const modified = typeof normalizedResult.modified === 'boolean' ? normalizedResult.modified : true;
        const replacements = typeof normalizedResult.replacements === 'number' ? normalizedResult.replacements : undefined;
        if (success && modified && (replacements == null || replacements > 0)) {
          agg.writeConfirmed = true;
        }
        const errMsg = typeof normalizedResult.error === 'string' ? normalizedResult.error : undefined;
        if (errMsg) agg.errors.push(errMsg);
        const msg = typeof normalizedResult.message === 'string' ? normalizedResult.message : undefined;
        if (msg && msg !== errMsg) agg.errors.push(msg);
      }

      if (normalizedResult && typeof normalizedResult === 'object' && typeof normalizedResult.error === 'string') {
        agg.errors.push(normalizedResult.error);
      }
    };

    if (typeof event === 'string') {
      agg.textChunks.push(event);
      return;
    }

    if (typeof event !== 'object') return;

    const type = String(event.type || '').toLowerCase();

    if (type === 'raw_model_stream_event') {
      const data = event.data || {};
      const delta = typeof data?.delta === 'string' ? data.delta : undefined;
      const text = typeof data?.text === 'string' ? data.text : undefined;
      const payload = delta ?? text;
      if (typeof payload === 'string' && payload.length > 0) {
        agg.textChunks.push(payload);
      }
      return;
    }

    if (type === 'tool_call') {
      const data = event.data || {};
      const name = typeof data?.name === 'string' ? data.name : 'tool';
      const callId = extractToolCallId(data);
      const argsRaw = data?.args;
      const parsedArgs = typeof argsRaw === 'string' ? safeJsonParse(argsRaw) ?? argsRaw : argsRaw;
      if (callId) {
        const existing = agg.toolCalls.find(call => call.id === callId);
        if (existing) {
          existing.name = name;
          existing.args = parsedArgs;
          existing.id = callId;
        } else {
          agg.toolCalls.push({ id: callId, name, args: parsedArgs });
        }
      } else {
        agg.toolCalls.push({ name, args: parsedArgs });
      }
      return;
    }

    if (type === 'tool_result') {
      const data = event.data || {};
      const name = typeof data?.name === 'string' ? data.name : 'tool';
      const callId = extractToolCallId(data);
      const resultRaw = data?.result;
      const parsedResult = typeof resultRaw === 'string' ? safeJsonParse(resultRaw) ?? resultRaw : resultRaw;
      recordToolResult(callId, name, parsedResult);
      return;
    }

    if (type === 'message' && typeof event.data?.message === 'string') {
      agg.textChunks.push(event.data.message);
      return;
    }

    const maybeText = typeof event?.text === 'string' ? event.text : undefined;
    if (maybeText) {
      agg.textChunks.push(maybeText);
    }
  }
