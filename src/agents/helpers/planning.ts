import { OrchestratorOptions, StreamStepAggregation } from '@/types/orchestrator-types';
import { newRunId, appendStep } from '@/utils/stepAggregator';
import { summarizeAgentResult } from '@/utils/summarizeAgentResult';
import { buildAgentInput } from '../PlanningAgent';
import { executeWithAgent } from '../agent-actions';
import { resolveStepAgent } from './agent-actions';
import { finalizeStreamAggregator, normalizeStreamCompletion, observeStreamEventForAggregation } from './streams';
import { formatAgentDisplayName } from './formatting';
import agents from '..';
import { normalizeAgentChoice } from './normalizers';

const createStreamAggregator = (agent: string): StreamStepAggregation => {
    return {
      agent,
      textChunks: [],
      toolCalls: [],
      writeConfirmed: false,
      errors: [],
    };
}

export const mapPlannedAgentToRegistered = (agentLabel: string | undefined): string => {
  if (!agentLabel || typeof agentLabel !== 'string') return 'ResearchAgent';
  const trimmed = agentLabel.trim();
  if (agents.getAgentMap().has(trimmed)) return trimmed;
  const direct = Array.from(agents.getAgentMap().keys()).find((name) => name.toLowerCase() === trimmed.toLowerCase());
  if (direct) return direct;
  const a = trimmed.toLowerCase();
  if (['research', 'researcher', 'analysis', 'analyze'].includes(a)) return 'ResearchAgent';
  if (['code', 'coding', 'implementation', 'build', 'code-generation', 'codegen'].includes(a)) return 'CodeGenerationAgent';
  if (['debug', 'fix', 'bugfix', 'troubleshoot'].includes(a)) return 'DebugAgent';
  if (['security', 'security-analysis', 'pentest', 'vuln'].includes(a)) return 'SecurityAnalysisAgent';
  if (['review', 'code-review', 'qa'].includes(a)) return 'CodeReviewAgent';
  if (['file-analysis', 'file analysis', 'analysis-worker', 'file risk', 'file verification'].includes(a)) return 'FileAnalysisAgent';
  if (['planning', 'plan'].includes(a)) return 'PlanningAgent';
  if (['workspace', 'google', 'google-workspace', 'gmail', 'calendar', 'drive', 'email', 'inbox'].includes(a)) return 'GoogleWorkspaceAgent';
  if (['computer', 'computer-use', 'browser', 'automation', 'web-automation'].includes(a)) return 'ComputerUseAgent';
  if (['isolated-computer-use', 'headless', 'background', 'monitoring'].includes(a)) return 'IsolatedComputerUseAgent';
  if (['diagnostic', 'healthcheck', 'diagnostics'].includes(a)) return 'DebugAgent';
  return 'ResearchAgent';
};

export const getStructuredPlan = async (input: string,  context?: any): Promise<{ summary?: string; steps: Array<any> }> => {
  const planningPrompt = `You are the PlanningAgent. Create a concise, structured execution plan in strict JSON.\n\nInput:\n${input}\n\nOutput JSON schema:\n{\n  "summary": string,\n  "steps": [\n    {\n      "id": string,\n      "title": string,\n      "description": string,\n      "agent": string,\n      "input": string,\n      "dependsOn": string[],\n      "acceptanceCriteria": string[]\n    }\n  ]\n}\n\nRules:\n- Return ONLY valid JSON. No prose, no markdown.\n- Keep steps minimal and actionable.`;
  const planningTimeoutMs = Number(context?.planningTimeoutMs ?? 60000);
  let plan: any;
  try {
    const mod = await import('../PlanningAgent');
    if (typeof (mod as any).createStructuredExecutionPlan === 'function') {
      plan = await Promise.race([
        (mod as any).createStructuredExecutionPlan(input, { planningDepth: 'detailed', context }),
        new Promise((_, rej) => setTimeout(() => rej(new Error('planning_timeout')), planningTimeoutMs))
      ]);
    } else {
      throw new Error('structured_plan_tool_unavailable');
    }
  } catch {
    const mod = await import('../PlanningAgent');
    const res = await Promise.race([
      (mod as any).planningAgent.execute(planningPrompt, { stream: false, planningDepth: 'detailed', context }),
      new Promise((_, rej) => setTimeout(() => rej(new Error('planning_timeout')), planningTimeoutMs))
    ]);
    const text = (res as any)?.text ?? String(res ?? '');
    const fence = text.match(/```[a-zA-Z]*\n([\s\S]*?)```/);
    const raw = fence ? fence[1] : text;
    plan = JSON.parse(raw);
  }
  if (!plan || !Array.isArray(plan.steps) || plan.steps.length === 0) throw new Error('PlanningAgent returned an empty or invalid plan');
  // Normalize and sanitize steps
  const normalizedSteps = plan.steps.map((s: any, i: number) => ({
    id: String(s.id ?? `step_${i + 1}`),
    title: s.title ? String(s.title) : undefined,
    description: String(s.description ?? s.task ?? ''),
    agent: String(s.agent ?? s.owner ?? 'research').toLowerCase(),
    input: s.input != null ? String(s.input) : undefined,
    dependsOn: Array.isArray(s.dependsOn) ? s.dependsOn.map((d: any) => String(d)) : [],
    focus: s.focus ? String(s.focus) : undefined,
    instructions: Array.isArray(s.instructions) ? s.instructions.map((instr: any) => String(instr)) : undefined,
    expectedOutputs: Array.isArray(s.expectedOutputs) ? s.expectedOutputs.map((o: any) => String(o)) : undefined,
    acceptanceCriteria: Array.isArray(s.acceptanceCriteria) ? s.acceptanceCriteria.map((a: any) => String(a)) : undefined,
    handoffNotes: s.handoffNotes ? String(s.handoffNotes) : undefined,
    references: Array.isArray(s.references) ? s.references.map((r: any) => String(r)) : undefined,
  }));

  // Drop planning-only steps when we have other executable work to prevent loops
  let filtered = normalizedSteps.filter((s: any) => {
    const mapped = mapPlannedAgentToRegistered(s.agent);
    return mapped !== 'PlanningAgent';
  });

  if (filtered.length === 0 && normalizedSteps.length > 0) {
    // If every step was mapped to PlanningAgent, reclassify them heuristically so
    // delegation still proceeds instead of returning an empty plan.
    filtered = normalizedSteps.map((s: any) => {
      const preliminary = mapPlannedAgentToRegistered(s.agent);
      const normalized = normalizeAgentChoice(s, preliminary);
      let mapped = mapPlannedAgentToRegistered(normalized);
      if (mapped === 'PlanningAgent') mapped = 'ResearchAgent';
      if (mapped === 'TaskOrchestrator') mapped = 'ResearchAgent';
      return { ...s, agent: mapped };
    });
  }

  // Deduplicate identical steps (same mapped agent + description + input)
  const seen = new Set<string>();
  const deduped: any[] = [];
  for (const s of filtered) {
    const mapped = mapPlannedAgentToRegistered(s.agent);
    const key = `${mapped}|${(s.title || '').trim()}|${(s.description || '').trim()}|${(s.input || '').trim()}`.toLowerCase();
    if (seen.has(key)) continue;
    seen.add(key);
    deduped.push(s);
  }
  const existingIds = new Set(deduped.map((s: any) => s.id));
  const codeReviewStepIds = deduped
    .filter((s: any) => mapPlannedAgentToRegistered(s.agent) === 'CodeReviewAgent')
    .map((s: any) => s.id);
  const hasFileAnalysisStep = deduped.some((s: any) => mapPlannedAgentToRegistered(s.agent) === 'FileAnalysisAgent');

  if (codeReviewStepIds.length > 0 && !hasFileAnalysisStep) {
    let counter = 1;
    let generatedId = `file_analysis_${codeReviewStepIds[codeReviewStepIds.length - 1] || 'summary'}`;
    while (existingIds.has(generatedId)) {
      generatedId = `file_analysis_${counter++}`;
    }
    deduped.push({
      id: generatedId,
      title: 'Post-change File Analysis',
      description: 'After code review completes, inspect each modified file to confirm no security, build, or regression risks remain. Use list_modified_files and run_file_analysis, incorporate code review findings, and recommend follow-up actions if issues remain.',
      agent: 'file-analysis',
      input: undefined,
      dependsOn: Array.from(new Set(codeReviewStepIds)),
      acceptanceCriteria: [
        'Summarize run_file_analysis findings for every modified file with explicit risk level.',
        'Call out any gaps still present after the code review and recommend remediation steps.',
        'Confirm whether the build/test state is safe or specify additional verification required.',
      ],
    });
  }

  plan.steps = deduped;
  return plan;
};

export const planAndDelegate = async (input: string, options: OrchestratorOptions & { maxWorkers?: number; context?: any; orchestratorInstance?: any } = {}): Promise<any> => {
    const runId = newRunId('task');
    const plan = await getStructuredPlan(input, options.context);
    try { appendStep(runId, { t: Date.now(), agent: 'TaskOrchestrator', step: { type: 'plan_created', summary: plan.summary, steps: plan.steps.map((s: any) => ({ id: s.id, agent: s.agent, dependsOn: s.dependsOn || [] })) } }); } catch {}

    // Get orchestrator instance - either from options or import default
    let orchestratorInstance = options.orchestratorInstance;
    if (!orchestratorInstance) {
      const { orchestrator } = await import('../DanteOrchestrator');
      orchestratorInstance = orchestrator;
    }

    if (options.stream) {
      const completedStepSummaries = new Map<string, string>();
      const planStepMeta = new Map<string, any>(plan.steps.map((s: any) => [s.id, { ...s }]));
      const streamingStepResults: Array<{ stepId: string; agent: string; success: boolean; summary?: string; output?: any; error?: string; meta?: any }> = [];
      const iterator = {
        async *[Symbol.asyncIterator]() {
          yield { type: 'plan_created', data: { summary: plan.summary, steps: plan.steps } };
          // Analyze plan for cycles and missing dependencies, and pre-mark blocked steps
          const stepsById = new Map<string, any>();
          for (const s of plan.steps) stepsById.set(s.id, s);
          const dependents = new Map<string, Set<string>>();
          const indegree = new Map<string, number>();
          for (const s of plan.steps) { dependents.set(s.id, new Set()); indegree.set(s.id, 0); }
          const unknownDeps = new Map<string, string[]>();
          for (const s of plan.steps) {
            const deps = Array.isArray(s.dependsOn) ? s.dependsOn : [];
            for (const d of deps) {
              if (!stepsById.has(d)) {
                const arr = unknownDeps.get(s.id) || [];
                arr.push(d);
                unknownDeps.set(s.id, arr);
              } else {
                indegree.set(s.id, (indegree.get(s.id) || 0) + 1);
                (dependents.get(d) as Set<string>).add(s.id);
              }
            }
          }
          const blocked = new Map<string, string>();
          for (const [sid, arr] of unknownDeps) blocked.set(sid, `missing_dependency: ${arr.join(', ')}`);
          const queue0: string[] = [];
          const visited0 = new Set<string>();
          for (const [sid, deg] of indegree) if ((deg || 0) === 0 && !blocked.has(sid)) queue0.push(sid);
          while (queue0.length) {
            const cur = queue0.shift() as string;
            visited0.add(cur);
            for (const dep of dependents.get(cur) || []) {
              if (blocked.has(dep)) continue;
              const nd = (indegree.get(dep) || 0) - 1;
              indegree.set(dep, nd);
              if (nd === 0) queue0.push(dep);
            }
          }
          for (const sid of stepsById.keys()) if (!visited0.has(sid) && !blocked.has(sid)) blocked.set(sid, 'cycle_detected');
          const cascadeQ: string[] = Array.from(blocked.keys());
          while (cascadeQ.length) {
            const b = cascadeQ.shift() as string;
            for (const d of dependents.get(b) || []) {
              if (!blocked.has(d)) { blocked.set(d, 'blocked_by_dependency'); cascadeQ.push(d); }
            }
          }
          type Active = {
            step: any;
            iterator: AsyncIterator<any>;
            agentInternal: string;
            agentDisplay: string;
            aggregator: StreamStepAggregation;
            next?: Promise<{ index: number; r: IteratorResult<any> }>;
            index: number;
            failedOnStart?: boolean;
            completion?: Promise<void>;
            completionHandled?: boolean;
            failureReason?: string;
          };
          const depsLeft = new Map<string, Set<string>>();
          for (const s of plan.steps) depsLeft.set(s.id, new Set(s.dependsOn || []));
          const done = new Set<string>();
          const active: Active[] = [];
          // Concurrency heuristic: if the plan has no explicit dependencies and includes
          // multiple code-generation steps, run sequentially to avoid file-edit collisions.
          const hasNoDeps = plan.steps.every((s: any) => !s.dependsOn || s.dependsOn.length === 0);
          const codegenCount = plan.steps.filter((s: any) => normalizeAgentChoice(s, mapPlannedAgentToRegistered(s.agent)) === 'CodeGenerationAgent').length;
          const unsafeParallel = hasNoDeps && codegenCount > 1;
          const requestedWorkers = Math.max(1, (options as any).maxWorkers ?? 2);
          const maxWorkers = unsafeParallel ? 1 : requestedWorkers;

          // Emit skipped markers for blocked steps so UI shows why they won't run
          for (const [sid, reason] of blocked.entries()) {
            const st = stepsById.get(sid);
            const agentInternal = normalizeAgentChoice(st, mapPlannedAgentToRegistered(st.agent));
            const agentDisplay = formatAgentDisplayName(agentInternal);
            yield { type: 'delegation_skipped', data: { stepId: sid, agent: agentDisplay, title: st.title || sid, description: st.description, reason } } as any;
          }

          const startStep = async (step: any): Promise<Active> => {
            const agentInternal = await resolveStepAgent(step, plan.summary);
            const agentDisplay = formatAgentDisplayName(agentInternal);
            let res: any; let failedOnStart = false;
            const aggregator = createStreamAggregator(agentInternal);
            try {
              const dependencySummaries = Array.isArray(step?.dependsOn)
                ? step.dependsOn.map((dep: any) => ({
                    id: String(dep),
                    summary: completedStepSummaries.get(String(dep)),
                    handoffNotes: planStepMeta.get(String(dep))?.handoffNotes,
                  }))
                : [];
              const inputForAgent = buildAgentInput(step, {
                title: step.title || step.id,
                dependencySummaries,
                planSummary: plan.summary,
                agentName: agentInternal,
              });
              res = await executeWithAgent(agentInternal, inputForAgent, {
                stream: true,
                maxSteps: Math.min(options.maxSteps ?? 8, 12),
                onStepFinish: (s: any) => {
                  try { appendStep(runId, { t: Date.now(), agent: agentInternal, step: s }); } catch {}
                  try { (options as any)?.onStepFinish?.(s); } catch {}
                },
              } as any, {orchestratorInstance} as any);
            } catch (e) { failedOnStart = true; res = { async *[Symbol.asyncIterator]() {} } as any; }
            const it = (res as any)[Symbol.asyncIterator] ? (res as any)[Symbol.asyncIterator]() : (res as any).textStream?.[Symbol.asyncIterator]?.();
            return {
              step,
              iterator: it,
              agentInternal,
              agentDisplay,
              aggregator,
              index: -1,
              failedOnStart,
              completion: normalizeStreamCompletion(res),
            };
          };

          const schedule = async (): Promise<any[]> => {
            const startEvents: any[] = [];
            while (active.length < maxWorkers) {
              const next = plan.steps.find(s => !done.has(s.id) && !active.find(a => a.step.id === s.id) && !blocked.has(s.id) && (depsLeft.get(s.id)?.size || 0) === 0);
              if (!next) break;
              const a = await startStep(next);
              a.index = active.length;
              active.push(a);
              startEvents.push({ type: 'delegation_start', data: { stepId: next.id, agent: a.agentDisplay, title: next.title || `${a.agentDisplay} Started`, description: `Processing delegated task` } } as any);
              a.next = a.iterator.next().then(r => ({ index: a.index, r }));
            }
            return startEvents;
          };

          for (const ev of await schedule()) { yield ev as any; }
          while (active.length) {
            const settled = await Promise.race(active.filter(a => a.next).map(a => a!.next!));
            if (!settled) break;
            const { index, r } = settled;
            const a = active[index];
            if (!a) continue;
            if (r.done) {
              // step done
              done.add(a.step.id);
              if (a.completion && !a.completionHandled) {
                try {
                  await a.completion;
                } catch (err: any) {
                  a.failureReason = err instanceof Error ? err.message : String(err ?? 'unknown stream error');
                } finally {
                  a.completionHandled = true;
                }
              }
              const aggregated = finalizeStreamAggregator(a.aggregator);
              const summary = summarizeAgentResult(aggregated.result, a.step);
              if (summary) {
                completedStepSummaries.set(a.step.id, summary);
                const metaEntry = planStepMeta.get(a.step.id);
                if (metaEntry) metaEntry.handoffNotes = summary;
              }
              const stepError = a.failureReason || aggregated.meta.errors[0];
              const success = !a.failedOnStart && !stepError;
              streamingStepResults.push({
                stepId: a.step.id,
                agent: a.agentDisplay,
                success,
                summary,
                output: aggregated.result,
                error: stepError,
                meta: aggregated.meta,
              });
              yield {
                type: 'delegation_end',
                data: { stepId: a.step.id, agent: a.agentDisplay, success, summary, error: stepError },
              } as any;
              // update dependents
              for (const s of plan.steps) { const deps = depsLeft.get(s.id); if (deps && deps.has(a.step.id)) deps.delete(a.step.id); }
              // remove
              active.splice(index, 1);
              for (let i = index; i < active.length; i++) active[i].index = i;
              for (const ev of await schedule()) { yield ev as any; }
            } else {
              observeStreamEventForAggregation(a.aggregator, r.value);
              yield r.value as any;
              a.next = a.iterator.next().then(rr => ({ index: a.index, r: rr }));
            }
          }
          yield { type: 'raw_model_stream_event', data: { type: 'output_text', text: `\n— Orchestration complete —` } } as any;
        }
      } as AsyncIterable<any>;
      (iterator as any).stepResults = streamingStepResults;
      (iterator as any).completedSummaries = completedStepSummaries;
      return iterator as any;
    }

    // Non-stream: run steps sequentially and synthesize a concise result
    const results: Array<{ stepId: string; agent: string; success: boolean; output?: any; summary?: string; error?: string; startedAt: number; completedAt: number; }>
      = [];
    // Compute blocked steps (cycles/missing deps) to skip gracefully
    const stepsByIdNS = new Map(plan.steps.map((s: any) => [s.id, s] as const));
    const dependentsNS = new Map<string, Set<string>>();
    const indegreeNS = new Map<string, number>();
    for (const s of plan.steps) { dependentsNS.set(s.id, new Set()); indegreeNS.set(s.id, 0); }
    const unknownDepsNS = new Map<string, string[]>();
    for (const s of plan.steps) {
      for (const d of (Array.isArray(s.dependsOn) ? s.dependsOn : [])) {
        if (!stepsByIdNS.has(d)) {
          const arr = unknownDepsNS.get(s.id) || [];
          arr.push(d);
          unknownDepsNS.set(s.id, arr);
        } else {
          indegreeNS.set(s.id, (indegreeNS.get(s.id) || 0) + 1);
          (dependentsNS.get(d) as Set<string>).add(s.id);
        }
      }
    }
    const blockedNS = new Map<string, string>();
    for (const [sid, arr] of unknownDepsNS) blockedNS.set(sid, `missing_dependency: ${arr.join(', ')}`);
    const qNS: string[] = [];
    const visitedNodes = new Set<string>();
    for (const [sid, deg] of indegreeNS) if ((deg || 0) === 0 && !blockedNS.has(sid)) qNS.push(sid);
    while (qNS.length) {
      const cur = qNS.shift() as string;
      visitedNodes.add(cur);
      for (const dep of dependentsNS.get(cur) || []) {
        if (blockedNS.has(dep)) continue;
        const nd = (indegreeNS.get(dep) || 0) - 1;
        indegreeNS.set(dep, nd);
        if (nd === 0) qNS.push(dep);
      }
    }
    for (const sid of stepsByIdNS.keys()) if (!visitedNodes.has(sid) && !blockedNS.has(sid)) blockedNS.set(sid, 'cycle_detected');
    const cas: string[] = Array.from(blockedNS.keys());
    while (cas.length) {
      const b = cas.shift() as string;
      for (const d of dependentsNS.get(b) || []) { if (!blockedNS.has(d)) { blockedNS.set(d, 'blocked_by_dependency'); cas.push(d); } }
    }

    const completedStepSummaries = new Map<string, string>();
    const planStepMeta = new Map<string, any>(plan.steps.map((s: any) => [s.id, s] as const));

    for (const step of plan.steps) {
      const agentInternal = await resolveStepAgent(step, plan.summary);
      const agentDisplay = formatAgentDisplayName(agentInternal);
      const startedAt = Date.now();
      try {
        if (blockedNS.has(step.id)) {
          results.push({ stepId: step.id, agent: agentDisplay, success: false, error: blockedNS.get(step.id) || 'blocked', startedAt, completedAt: Date.now() });
          continue;
        }
        const dependencySummaries = Array.isArray((step as any)?.dependsOn)
          ? (step as any).dependsOn.map((dep: any) => ({
              id: String(dep),
              summary: completedStepSummaries.get(String(dep)),
              handoffNotes: planStepMeta.get(String(dep))?.handoffNotes,
            }))
          : [];
        const inputForAgent = buildAgentInput(step as any, {
          title: step.title || step.id,
          dependencySummaries,
          planSummary: plan.summary,
          agentName: agentInternal,
        });
        // Forward onStepFinish so the API layer can surface tool events during delegated steps
        const res = await executeWithAgent(
          agentInternal,
          inputForAgent,
          {
            stream: false,
            maxSteps: Math.min(options.maxSteps ?? 8, 12),
            onStepFinish: (s: any) => {
              try { appendStep(runId, { t: Date.now(), agent: agentInternal, step: s }); } catch {}
              try { (options as any)?.onStepFinish?.(s); } catch {}
            },
          } as any,
          {orchestratorInstance} as any,
        );
        const summary = summarizeAgentResult(res, step);
        completedStepSummaries.set(step.id, summary);
        const metaEntry = planStepMeta.get(step.id);
        if (metaEntry) metaEntry.handoffNotes = summary;
        results.push({ stepId: step.id, agent: agentDisplay, success: true, output: res, summary, startedAt, completedAt: Date.now() });
      } catch (e: any) {
        results.push({ stepId: step.id, agent: agentDisplay, success: false, error: e?.message || String(e), startedAt, completedAt: Date.now() });
      }
    }
    // Optional synthesis
    let synthesizedText = '';
    try {
      const { synthesisWorker } = await import('../workers/SynthesisWorker');
      const workerResultsPayload = results.map(({ output, ...rest }) => ({ ...rest, result: output }));
      const synthesizedResult = await synthesisWorker.execute(
        JSON.stringify({ originalTask: input, workerResults: workerResultsPayload }),
        { context: { taskType: 'synthesis', originalTask: input }, synthesisType: 'detailed', prioritizeInsights: true }
      );
      synthesizedText = typeof synthesizedResult?.synthesizedResponse === 'string' ? synthesizedResult.synthesizedResponse : JSON.stringify(synthesizedResult);
    } catch {
      synthesizedText = results.map(r => `- [${r.success ? 'ok' : 'fail'}] ${r.agent} (${r.stepId})`).join('\n');
    }
    return { type: 'delegated_orchestration', originalTask: input, text: synthesizedText, finalMessage: synthesizedText, runId, plan, stepResults: results };
  };

export const buildStepRoutingBasis =(
  step: {
    id?: string;
    title?: string;
    description?: string;
    input?: string;
    acceptanceCriteria?: string[];
    dependsOn?: string[];
  },
  planSummary?: string,
): string => {
  const lines: string[] = [];
  if (planSummary && planSummary.trim()) lines.push(`Plan summary: ${planSummary.trim()}`);
  const id = step.id ? ` (${step.id})` : '';
  if (step.title && step.title.trim()) lines.push(`Step title${id}: ${step.title.trim()}`);
  if (step.description && step.description.trim()) lines.push(`Step description: ${step.description.trim()}`);
  if (step.input && step.input.trim()) lines.push(`Provided input: ${step.input.trim()}`);
  if (Array.isArray(step?.dependsOn) && step.dependsOn.length > 0) {
    lines.push(`Depends on steps: ${step.dependsOn.join(', ')}`);
  }
  if (Array.isArray(step?.acceptanceCriteria) && step.acceptanceCriteria.length > 0) {
    lines.push('Acceptance criteria:');
    for (const crit of step.acceptanceCriteria) {
      if (typeof crit === 'string' && crit.trim()) {
        lines.push(`- ${crit.trim()}`);
      }
    }
  }
  lines.push('Choose the single best execution agent for this step. Prefer specialized workers and avoid TaskOrchestrator or PlanningAgent for execution.');
  return lines.join('\n');
};
