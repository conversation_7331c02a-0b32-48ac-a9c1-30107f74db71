import { Database } from "bun:sqlite";
import path from "node:path";
import fs from "node:fs";
import { Item } from "./types";

const DB_PATH = process.env.BRIEFING_DB_PATH || path.join(process.cwd(), "data", "briefing.sqlite");

let dbInstance: Database | null = null;

export function ensureDb(): Database {
  if (dbInstance) {
    return dbInstance;
  }
  fs.mkdirSync(path.dirname(DB_PATH), { recursive: true });
  const db = new Database(DB_PATH);
  dbInstance = db;
  return db;
}

export function initDb(db: Database) {
  db.run(`
    CREATE TABLE IF NOT EXISTS items (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      url TEXT NOT NULL,
      source TEXT NOT NULL,
      section TEXT NOT NULL,
      publishedAt INTEGER NOT NULL,
      summary TEXT,
      meta TEXT
    );
  `);
  db.run(`
    CREATE TABLE IF NOT EXISTS runs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      startedAt INTEGER NOT NULL,
      finishedAt INTEGER,
      fetched INTEGER DEFAULT 0,
      validated INTEGER DEFAULT 0,
      inserted INTEGER DEFAULT 0,
      validationFailed INTEGER DEFAULT 0,
      upsertFailed INTEGER DEFAULT 0
    );
  `);
  // Ensure required indexes exist for performance and data integrity
  db.run('CREATE INDEX IF NOT EXISTS idx_items_publishedAt ON items (publishedAt DESC)');

  // Deduplicate rows by URL before creating a unique index on url.
  // This removes any rows with duplicate url values while keeping the first row per url (by rowid).
  try {
    const dupRow = db.query<any, []>(
      "SELECT COUNT(*) as count FROM items WHERE rowid NOT IN (SELECT MIN(rowid) FROM items GROUP BY url)"
    ).get();
    const duplicates = dupRow && typeof dupRow.count === 'number' ? dupRow.count : Number(dupRow?.count ?? 0);

    if (duplicates > 0) {
      // Delete duplicate rows, keep the row with the smallest rowid for each url
      db.run("DELETE FROM items WHERE rowid NOT IN (SELECT MIN(rowid) FROM items GROUP BY url)");
      console.log(`[briefing] Deduplicated items by url; deleted ${duplicates} duplicate row(s).`);
    } else {
      console.log('[briefing] No duplicate items by url found.');
    }
  } catch (err) {
    // If anything goes wrong during deduplication, surface a helpful message but continue to attempt index creation.
    console.error('[briefing] Error during deduplication:', err);
  }

  // Create unique index on url after deduplication to enforce uniqueness going forward
  db.run('CREATE UNIQUE INDEX IF NOT EXISTS idx_items_url ON items (url)');
}

let _upsertItemStmt: ReturnType<Database['prepare']> | null = null;
let _getItemByIdQuery: ReturnType<Database['query']> | null = null;
let _listItemsSinceQuery: ReturnType<Database['query']> | null = null;
let _startRunStmt: ReturnType<Database['prepare']> | null = null;
let _finishRunStmt: ReturnType<Database['prepare']> | null = null;

export function initializeStatements(db: Database) {
  _upsertItemStmt = db.prepare(`
    INSERT INTO items (id, title, url, source, section, publishedAt, summary, meta)
    VALUES ($id, $title, $url, $source, $section, $publishedAt, $summary, $meta)
    ON CONFLICT(id) DO UPDATE SET
      title=excluded.title, url=excluded.url, source=excluded.source,
      section=excluded.section, publishedAt=excluded.publishedAt,
      summary=excluded.summary, meta=excluded.meta
  `);
  _getItemByIdQuery = db.query<{ id: string }, []>("SELECT id FROM items WHERE id = $id");
  _listItemsSinceQuery = db.query<Omit<Item, 'publishedAt' | 'meta'> & { publishedAt: number; meta: string | null }, [number]>(
    `SELECT * FROM items WHERE publishedAt >= $sinceMs ORDER BY publishedAt DESC`
  );
  _startRunStmt = db.prepare(`INSERT INTO runs (startedAt) VALUES ($startedAt)`);
  _finishRunStmt = db.prepare(`
    UPDATE runs SET finishedAt=$finishedAt, fetched=$fetched, validated=$validated,
    inserted=$inserted, validationFailed=$validationFailed, upsertFailed=$upsertFailed
    WHERE id=$id
  `);
}

export function upsertItem(db: Database, item: Item): { inserted: boolean; updated: boolean } {
  if (!_upsertItemStmt || !_getItemByIdQuery) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");
  
  const existing = _getItemByIdQuery.get({
    $id: item.id,
  });
  const res = _upsertItemStmt.run({
    $id: item.id,
    $title: item.title,
    $url: item.url,
    $source: item.source,
    $section: item.section,
    $publishedAt: item.publishedAt.getTime(),
    $summary: item.summary || null,
    $meta: item.meta ? JSON.stringify(item.meta) : null,
  });
  return { inserted: res.changes > 0 && !existing, updated: res.changes > 0 && !!existing };
}

export function bulkUpsertItems(db: Database, items: Item[]): { inserted: number; updated: number } {
  let inserted = 0;
  let updated = 0;

  const transaction = db.transaction((itemsToUpsert: Item[]) => {
    for (const item of itemsToUpsert) {
      const { inserted: itemInserted, updated: itemUpdated } = upsertItem(db, item);
      if (itemInserted) {
        inserted++;
      }
      if (itemUpdated) {
        updated++;
      }
    }
  });

  transaction(items);
  return { inserted, updated };
}

export function listItemsSince(db: Database, sinceMs: number): Item[] {
  if (!_listItemsSinceQuery) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  const rows = _listItemsSinceQuery.all({ $sinceMs: sinceMs });

  return rows.map(r => ({
    id: r.id,
    title: r.title,
    url: r.url,
    source: r.source,
    section: r.section,
    publishedAt: new Date(r.publishedAt),
    summary: r.summary || undefined,
    meta: r.meta ? JSON.parse(r.meta) : undefined,
  }));
}

export function startRun(db: Database): number {
  if (!_startRunStmt) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  const res = _startRunStmt.run({ $startedAt: Date.now() });
  return res.lastInsertRowid as number;
}

export function finishRun(
  db: Database,
  runId: number,
  stats: { fetched: number; validated: number; inserted: number; validationFailed: number; upsertFailed: number }
) {
  if (!_finishRunStmt) throw new Error("Database statements not initialized. Call initializeStatements(db) first.");

  _finishRunStmt.run({
    $finishedAt: Date.now(),
    $fetched: stats.fetched,
    $validated: stats.validated,
    $inserted: stats.inserted,
    $validationFailed: stats.validationFailed,
    $upsertFailed: stats.upsertFailed,
    $id: runId,
  });
}