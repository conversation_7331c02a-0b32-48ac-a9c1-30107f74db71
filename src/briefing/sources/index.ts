import { <PERSON><PERSON><PERSON>, Section, Item } from "../types";
import { fetchRss } from "./rss";
import { fetchGitHubReleases } from "./githubAtom";
import { fetchFilteredRedditRss } from "./redditRss";
import { fetchRemoteOk } from "./remoteok";
import { normalizeRssItems, normalizeRemoteOk } from "../normalize";

export async function loadAllSources(): Promise<Item[]> {
  const [
    flutterBlogRes,
    dartBlogRes,
    flutterRelRes,
    dartRelRes,
    pubDevRes,
    redditJobsRes,
    remoteOkRes,
  ] = await Promise.allSettled([
    fetchRss("https://medium.com/feed/flutter"),
    fetchRss("https://medium.com/feed/dartlang"),
    fetchGitHubReleases("https://github.com/flutter/flutter/releases.atom"),
    fetchGitHubReleases("https://github.com/dart-lang/sdk/releases.atom"),
    fetchRss("https://pub.dev/feed.atom"),
    fetchFilteredRedditRss(
      "https://www.reddit.com/r/flutterdev/.rss",
      ["hiring", "job", "contract", "remote"]
    ),
    fetchRemoteOk("flutter"),
  ]);

  // Gracefully handle individual source failures without aborting ingestion
  const flutterBlog =
    flutterBlogRes.status === "fulfilled"
      ? flutterBlogRes.value
      : (console.warn(
          "[sources] Failed to fetch Flutter Blog RSS:",
          (flutterBlogRes as PromiseRejectedResult).reason
        ),
        []);

  const dartBlog =
    dartBlogRes.status === "fulfilled"
      ? dartBlogRes.value
      : (console.warn(
          "[sources] Failed to fetch Dart Blog RSS:",
          (dartBlogRes as PromiseRejectedResult).reason
        ),
        []);

  const flutterRel =
    flutterRelRes.status === "fulfilled"
      ? flutterRelRes.value
      : (console.warn(
          "[sources] Failed to fetch Flutter Releases (GitHub Atom):",
          (flutterRelRes as PromiseRejectedResult).reason
        ),
        []);

  const dartRel =
    dartRelRes.status === "fulfilled"
      ? dartRelRes.value
      : (console.warn(
          "[sources] Failed to fetch Dart Releases (GitHub Atom):",
          (dartRelRes as PromiseRejectedResult).reason
        ),
        []);

  const pubDev =
    pubDevRes.status === "fulfilled"
      ? pubDevRes.value
      : (console.warn(
          "[sources] Failed to fetch pub.dev feed:",
          (pubDevRes as PromiseRejectedResult).reason
        ),
        []);

  const redditJobs =
    redditJobsRes.status === "fulfilled"
      ? redditJobsRes.value
      : (console.warn(
          "[sources] Failed to fetch r/flutterdev jobs RSS:",
          (redditJobsRes as PromiseRejectedResult).reason
        ),
        []);

  const remoteOk =
    remoteOkRes.status === "fulfilled"
      ? remoteOkRes.value
      : (console.warn(
          "[sources] Failed to fetch RemoteOK jobs:",
          (remoteOkRes as PromiseRejectedResult).reason
        ),
        []);

  const items: Item[] = [
    ...normalizeRssItems(
      flutterBlog,
      SourceKey.FlutterBlog,
      Section.Headlines
    ),
    ...normalizeRssItems(dartBlog, SourceKey.DartBlog, Section.Headlines),
    ...normalizeRssItems(
      flutterRel,
      SourceKey.FlutterReleases,
      Section.Releases
    ),
    ...normalizeRssItems(dartRel, SourceKey.DartReleases, Section.Releases),
    ...normalizeRssItems(pubDev, SourceKey.PubDev, Section.Ecosystem),
    ...normalizeRssItems(
      redditJobs,
      SourceKey.RedditFlutterDev,
      Section.Jobs
    ),
    ...normalizeRemoteOk(remoteOk),
  ];

  // Basic sanity filter
  return items.filter((i) => i.id && i.url && i.title);
}
