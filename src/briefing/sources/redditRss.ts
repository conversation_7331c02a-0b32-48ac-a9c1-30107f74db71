import type { Output } from "rss-parser";
import { fetchRss } from "./rss";

export async function fetchFilteredRedditRss(
  url: string,
  keywords: string[]
): Promise<Output<any>> {
  const feed = await fetchRss(url);
  const lower = keywords.map((k) => k.toLowerCase());

  const filteredItems = (feed.items ?? []).filter((i) => {
    const t = (i.title ?? "").toLowerCase();
    return lower.some((k) => t.includes(k));
  });

  // Return a new feed object to preserve immutability of the original input
  const newFeed: Output<any> = {
    ...(feed as any),
    items: filteredItems,
  };

  return newFeed;
}
