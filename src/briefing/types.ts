import { z } from "zod";

export enum Section {
  Headlines = "Headlines",
  Releases = "Releases",
  Ecosystem = "Ecosystem",
  Jobs = "Jobs",
  Community = "Community",
}

export enum SourceKey {
  FlutterBlog = "flutter_blog",
  DartBlog = "dart_blog",
  FlutterReleases = "flutter_releases",
  DartReleases = "dart_releases",
  PubDev = "pub_dev",
  RedditFlutterDev = "reddit_flutterdev",
  RemoteOK = "remoteok",
}

export const ItemSchema = z.object({
  id: z.string(),
  title: z.string(),
  url: z
    .string()
    .url()
    .refine(
      (val) => {
        try {
          const protocol = new URL(val).protocol;
          return protocol === "http:" || protocol === "https:";
        } catch {
          return false;
        }
      },
      { message: "URL must use http or https" }
    ),
  source: z.nativeEnum(SourceKey),
  section: z.nativeEnum(Section),
  publishedAt: z.coerce.date(),
  summary: z.string().optional(),
  meta: z.record(z.string(), z.unknown()).optional(),
});

export type Item = z.infer<typeof ItemSchema>;
