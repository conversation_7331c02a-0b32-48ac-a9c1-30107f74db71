#!/usr/bin/env bun
import { composeBriefing } from "@/briefing/compose";
import { listItemsSince } from "@/briefing/db";
import fs from "node:fs";
import path from "node:path";

const mode = process.argv[2] || "daily";
const now = Date.now();
const windowMs = mode === "weekly" ? 7 * 24 * 3600_000 : 24 * 3600_000;
const since = now - windowMs;

const dateLabel = new Date(now).toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" });
const outDir = path.join(process.cwd(), "out");

async function main() {
  const items = listItemsSince(since);
  const res = composeBriefing(dateLabel, items);
  fs.mkdirSync(outDir, { recursive: true });
  fs.writeFileSync(path.join(outDir, `briefing-${mode}.md`), res.markdown);
  fs.writeFileSync(path.join(outDir, `briefing-${mode}.html`), res.html);
  console.log(`Wrote ${path.join("out", `briefing-${mode}.md`)} and ${path.join("out", `briefing-${mode}.html`)}`);
  console.log(`Subject: ${res.subject}`);
}
main();
