# Reducing File Edit Errors: Best Practices for AI Agents

## Problem Overview

A common error pattern occurs when agents attempt file edits that fail, then fall back to destructive full-file overwrites:

```
1. Agent tries: file_edit with operation="replace" and truncated/incorrect pattern
2. Tool returns: "Pattern not found; no changes applied" (pattern_not_found)
3. Agent falls back to: file_edit with operation="write" mode="overwrite"
4. Result: Entire file is overwritten, potentially losing changes
```

## Root Causes

### 1. **Truncated Content in Tool Arguments**
- LLMs may truncate long content strings with `…` 
- Truncated patterns will never match actual file content
- This is especially common when trying to pass entire file contents

### 2. **Improper Operation Selection**
- Using `replace` operation for multi-line changes
- Using `write` with `overwrite` as a fallback instead of proper error handling
- Not using the appropriate operation for the task

### 3. **Insufficient File Context**
- Not reading the file first to verify exact content
- Not checking what actually exists before attempting edits
- Making assumptions about file structure

### 4. **Lack of Verification**
- Not verifying the pattern exists before attempting replace
- Not checking edit results
- No retry strategy with corrected parameters

## Solutions & Best Practices

### 1. **Choose the Right Operation**

#### Use `patch` with `before`/`after` for Targeted Edits
```json
{
  "operation": "patch",
  "patchFormat": "anchored",
  "filePath": "src/file.ts",
  "before": "const oldValue = 123;",
  "after": "const oldValue = 456;",
  "scope": "one"
}
```

**Benefits:**
- More precise than `replace`
- Better for multi-line changes
- Clearer intent

#### Use `replace` Only for Simple String Substitution
```json
{
  "operation": "replace",
  "filePath": "src/file.ts",
  "pattern": "oldString",
  "replacement": "newString",
  "scope": "one"
}
```

**When to use:**
- Single-line, simple text replacement
- Pattern is short and unambiguous
- You've verified the pattern exists

#### Avoid `write` with `overwrite` Unless Necessary
```json
{
  "operation": "write",
  "filePath": "src/file.ts",
  "content": "...",
  "mode": "overwrite"
}
```

**Only use when:**
- Creating a new file
- Completely rewriting a file is the explicit intent
- You have the complete, correct content

### 2. **Always Read Before Editing**

```typescript
// GOOD: Read first, then edit
1. read_file({ filePath: "src/file.ts" })
2. Analyze the content
3. file_edit({ operation: "patch", before: "...", after: "..." })

// BAD: Edit without reading
1. file_edit({ operation: "replace", pattern: "...", replacement: "..." })
```

### 3. **Use Precise, Untruncated Patterns**

```json
// GOOD: Short, precise pattern
{
  "operation": "patch",
  "before": "const DB_PATH = process.env.BRIEFING_DB_PATH",
  "after": "const DB_PATH = process.env.BRIEFING_DB_PATH || '/default/path'"
}

// BAD: Long, potentially truncated pattern
{
  "operation": "replace",
  "pattern": "import { Database } from \"bun:sqlite\";\nimport path from \"node:path\";\nimport fs from \"node:fs\";\nimport { Item } from \"./types\";\n\nconst DB_PATH = process.env.BRIEFING_DB_PATH || path.join(process.cwd(),…",
  "replacement": "..."
}
```

### 4. **Handle Errors Gracefully**

```typescript
// GOOD: Proper error handling
if (edit_result.error === "pattern_not_found") {
  // Read the file to see what's actually there
  const content = read_file({ filePath: "..." });
  // Adjust the pattern based on actual content
  // Try again with corrected pattern
}

// BAD: Destructive fallback
if (edit_result.error === "pattern_not_found") {
  // Just overwrite the whole file
  file_edit({ operation: "write", mode: "overwrite", content: "..." });
}
```

### 5. **Use Unified Diff for Complex Multi-File Changes**

For complex edits involving multiple files or large changes:

```json
{
  "operation": "patch",
  "patchFormat": "unified_diff",
  "diff": "--- a/src/file.ts\n+++ b/src/file.ts\n@@ -10,3 +10,4 @@\n-old line\n+new line"
}
```

### 6. **Leverage Tool Features**

#### Use `idempotent` flag
```json
{
  "operation": "patch",
  "idempotent": true,
  "before": "old",
  "after": "new"
}
```
- If the change is already applied, returns success instead of error

#### Use `diagnostics` flag
```json
{
  "operation": "patch",
  "diagnostics": true,
  "before": "old",
  "after": "new"
}
```
- Runs syntax checks after edit
- Catches errors immediately

#### Use `ifMatchHash` for safety
```json
{
  "operation": "patch",
  "ifMatchHash": "abc123...",
  "before": "old",
  "after": "new"
}
```
- Ensures file hasn't changed since you read it
- Prevents race conditions

## Agent Instruction Guidelines

### For All File-Editing Agents

Add these guidelines to agent system prompts:

```
File Editing Best Practices:
1. **Always read files before editing** - Use read_file to see exact content
2. **Use 'patch' operation for targeted changes** - Prefer operation='patch' with before/after over replace
3. **Keep patterns short and precise** - Use the smallest unique string that identifies the change location
4. **Never overwrite files as error recovery** - If an edit fails, read the file and try again with corrected parameters
5. **Verify edits succeeded** - Check the tool result and read the file again if needed
6. **Use multi-line anchored patches** - For changes spanning multiple lines, use patch with before/after containing context lines
7. **Avoid truncation** - Never use '…' or truncate content in tool arguments

Error Recovery Strategy:
- If pattern_not_found: Read the file, find the correct pattern, retry with exact match
- If syntax errors after edit: Read the file, identify the issue, apply corrective patch
- If uncertain: Ask for clarification rather than guessing
```

### Specific to DebugAgent

The DebugAgent should emphasize:
- Reading files to understand current state
- Making minimal, targeted fixes
- Verifying fixes don't introduce new issues

## Tool-Level Improvements

### 1. **Add Warning for Large Patterns**

Modify `file_edit` tool to warn when pattern length exceeds threshold:

```typescript
if (pattern && pattern.length > 500) {
  return makeError('file_edit', 
    'Pattern too long (>500 chars). Use patch operation with before/after or unified_diff instead.',
    'pattern_too_long'
  );
}
```

### 2. **Detect Truncation**

```typescript
if (pattern && (pattern.includes('…') || pattern.endsWith('...'))) {
  return makeError('file_edit',
    'Pattern appears truncated. Provide complete, untruncated pattern.',
    'truncated_pattern'
  );
}
```

### 3. **Suggest Better Operation**

When `replace` fails, include suggestion in error:

```typescript
return makeError('file_edit', 
  'Pattern not found. Consider: 1) Read file first to verify content, 2) Use patch operation with before/after, 3) Use shorter, more specific pattern',
  'pattern_not_found',
  { 
    path: absolutePath, 
    modified: false, 
    replacements: 0,
    suggestion: 'Use operation=patch with before/after for multi-line changes'
  }
);
```

### 4. **Add Dry-Run Mode**

The tool already supports `dryRun` - encourage agents to use it:

```json
{
  "operation": "patch",
  "dryRun": true,
  "before": "...",
  "after": "..."
}
```

Returns preview without applying changes.

## Monitoring & Metrics

Track these metrics to identify problematic patterns:

1. **Replace → Write Fallback Rate**: How often agents fall back to overwrite after failed replace
2. **Pattern Not Found Rate**: Frequency of pattern_not_found errors
3. **Truncation Detection Rate**: How often truncated patterns are detected
4. **Read-Before-Edit Rate**: Percentage of edits preceded by file reads
5. **Edit Success Rate by Operation**: Success rates for write/replace/patch operations

## Summary

**Key Takeaways:**
1. ✅ **Read files before editing**
2. ✅ **Use `patch` operation for most edits**
3. ✅ **Keep patterns short and precise**
4. ✅ **Handle errors by reading and retrying, not overwriting**
5. ✅ **Verify edits succeeded**
6. ❌ **Never use truncated content (`…`) in patterns**
7. ❌ **Avoid `write` with `overwrite` as error recovery**
8. ❌ **Don't guess - read and verify**

By following these practices, agents will make fewer destructive errors and produce more reliable file edits.

