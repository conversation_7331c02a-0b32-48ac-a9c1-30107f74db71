# File Edit Error Prevention - Implementation Summary

## Problem Statement

A frequent error pattern was observed where file-editing agents (particularly DebugAgent) would:
1. Attempt a `replace` operation with truncated or incorrect patterns
2. Receive a `pattern_not_found` error
3. Fall back to a destructive `write` operation with `mode='overwrite'`
4. Overwrite the entire file, potentially losing changes

Example error sequence:
```
🛠️ Tool call | Agent: DebugAgent | Tool: file_edit | Args: {"operation":"replace","filePath":"src/briefing/db.ts","content":"import { Database } from \"bun:sqlite\";\nimport path from \"node:path\";\nimport fs from \"node:fs\";\nimport { Item } from \"./types\";\n\nconst DB_PATH = process.env.BRIEFING_DB_PATH || path.join(process.cwd(),…"}
🧩 Tool result | Agent: DebugAgent | Tool: file_edit | Result: {"success":false,"error":{"message":"Pattern not found; no changes applied","code":"pattern_not_found"}}
🛠️ Tool call | Agent: DebugAgent | Tool: file_edit | Args: {"operation":"write","filePath":"src/briefing/db.ts","content":"..."}
✅ File edit verified successfully (entire file overwritten)
```

## Root Causes Identified

1. **Truncated Content**: LLMs truncating long patterns with `…` that will never match
2. **Wrong Operation Choice**: Using `replace` for multi-line changes instead of `patch`
3. **Dangerous Fallback**: Falling back to full file overwrite after failed edits
4. **Insufficient Context**: Not reading files before attempting edits
5. **Lack of Guidance**: Agent instructions didn't emphasize proper file editing practices

## Solutions Implemented

### 1. Enhanced Agent Instructions

Updated both **DebugAgent** and **CodeGenerationAgent** with comprehensive file editing best practices:

**Files Modified:**
- `src/agents/vercel/debug.ts` (lines 44-78)
- `src/agents/BuiltInAgents.ts` (lines 54-98)

**New Guidelines Added:**
```
File Editing Best Practices (CRITICAL):
1. Always read files before editing - Use read_file to see exact content first
2. Use 'patch' operation for targeted changes - Prefer operation='patch' with before/after over replace
3. Keep patterns short and precise - Use the smallest unique string that identifies the change location
4. Never overwrite files as error recovery - If an edit fails, read the file and retry with corrected parameters
5. Verify edits succeeded - Check the tool result and read the file again if needed
6. Use multi-line anchored patches - For changes spanning multiple lines, use patch with before/after
7. Avoid truncation - Never use '…' or truncate content in tool arguments

Error Recovery Strategy:
- If pattern_not_found: Read the file, find the correct pattern, retry with exact match
- If syntax errors after edit: Read the file, identify the issue, apply corrective patch
- If uncertain: Ask for clarification rather than guessing
- NEVER fall back to operation='write' with mode='overwrite' after a failed edit
```

### 2. Tool-Level Validation

Enhanced `file_edit` tool with proactive error detection:

**File Modified:** `src/tools/fileOperations.ts`

**Validations Added:**

#### A. Truncation Detection (lines 707-714, 813-820)
```typescript
// Check for truncation indicators
if (patternStr.includes('…') || patternStr.includes('...') && patternStr.endsWith('...')) {
  return makeError('file_edit', 
    'Pattern appears truncated (contains … or ends with ...). Provide complete, untruncated pattern. Consider using operation=patch with before/after instead.',
    'truncated_pattern',
    { path: absolutePath, modified: false, replacements: 0 }
  );
}
```

#### B. Pattern Length Warning (lines 716-723)
```typescript
// Warn about excessively long patterns
if (!useRegex && patternStr.length > 500) {
  return makeError('file_edit',
    'Pattern too long (>500 chars). For multi-line changes, use operation=patch with before/after, or operation=patch with patchFormat=unified_diff.',
    'pattern_too_long',
    { path: absolutePath, modified: false, replacements: 0, patternLength: patternStr.length }
  );
}
```

#### C. Enhanced Error Messages (lines 753-768, 849-864)
```typescript
// For replace operations
return makeError('file_edit', 
  'Pattern not found; no changes applied. Suggestions: 1) Use read_file to verify exact content, 2) Use operation=patch with before/after for multi-line changes, 3) Use a shorter, more specific pattern',
  'pattern_not_found',
  { 
    path: absolutePath, 
    modified: false, 
    replacements: 0,
    patternLength: patternStr.length,
    suggestion: 'Read the file first to verify content, then use operation=patch with before/after'
  }
);

// For patch operations
return makeError('file_edit',
  'Before segment not found; no changes applied. Suggestion: Use read_file to verify exact content, then provide the exact before string that exists in the file.',
  'pattern_not_found',
  { 
    path: absolutePath, 
    modified: false, 
    replacements: 0,
    beforeLength: beforeStr.length,
    suggestion: 'Read the file first to verify the exact before content'
  }
);
```

### 3. Comprehensive Documentation

Created detailed documentation for developers and AI agents:

**Files Created:**
- `docs/reducing-file-edit-errors.md` - Complete guide with examples and best practices
- `docs/file-edit-error-prevention-summary.md` - This summary document

**Documentation Covers:**
- Root cause analysis
- Proper operation selection (write vs replace vs patch)
- Read-before-edit workflow
- Error handling strategies
- Tool feature usage (idempotent, diagnostics, ifMatchHash)
- Monitoring metrics

## Expected Impact

### Immediate Benefits
1. **Prevents Truncation Errors**: Tool now rejects truncated patterns before attempting edits
2. **Guides Better Choices**: Error messages suggest appropriate alternatives
3. **Reduces Overwrites**: Agents instructed to never use overwrite as fallback
4. **Improves Success Rate**: Emphasis on reading files first increases match accuracy

### Long-term Improvements
1. **Better Agent Behavior**: Agents learn proper file editing patterns through reinforced instructions
2. **Fewer Destructive Errors**: Reduced risk of accidental file overwrites
3. **Clearer Debugging**: Enhanced error messages help diagnose issues faster
4. **Consistent Patterns**: All file-editing agents follow same best practices

## Verification

### Type Checking
```bash
bun run typecheck
```
✅ Passed - No TypeScript errors introduced

### Recommended Testing
1. **Unit Tests**: Test truncation detection and pattern length validation
2. **Integration Tests**: Verify agents follow new guidelines
3. **Monitoring**: Track metrics for:
   - `pattern_not_found` error rate
   - `truncated_pattern` detection rate
   - Replace → Write fallback rate
   - Read-before-edit compliance rate

## Usage Examples

### ✅ GOOD: Proper patch operation
```json
{
  "operation": "patch",
  "patchFormat": "anchored",
  "filePath": "src/file.ts",
  "before": "const oldValue = 123;",
  "after": "const oldValue = 456;"
}
```

### ✅ GOOD: Read first, then edit
```typescript
1. read_file({ filePath: "src/file.ts" })
2. Analyze content
3. file_edit({ operation: "patch", before: "exact_match", after: "new_value" })
```

### ❌ BAD: Long truncated pattern
```json
{
  "operation": "replace",
  "pattern": "import { Database } from \"bun:sqlite\";\nimport path from \"node:path\";\nimport fs from \"node:fs\";\nimport { Item } from \"./types\";\n\nconst DB_PATH = process.env.BRIEFING_DB_PATH || path.join(process.cwd(),…",
  "replacement": "..."
}
```
**Result**: `truncated_pattern` error

### ❌ BAD: Overwrite as fallback
```typescript
if (edit_result.error === "pattern_not_found") {
  file_edit({ operation: "write", mode: "overwrite", content: "..." });
}
```
**Result**: Violates agent instructions, risks data loss

## Monitoring Recommendations

Track these metrics to measure effectiveness:

1. **Error Rates**
   - `pattern_not_found` errors (should decrease)
   - `truncated_pattern` errors (new metric)
   - `pattern_too_long` errors (new metric)

2. **Operation Usage**
   - `patch` vs `replace` vs `write` operation distribution
   - Read-before-edit pattern compliance

3. **Success Rates**
   - First-attempt edit success rate
   - Retry success rate after error

4. **Fallback Patterns**
   - Replace → Write fallback occurrences (should approach zero)
   - Edit → Read → Retry patterns (should increase)

## Next Steps

### Immediate
1. ✅ Update agent instructions (completed)
2. ✅ Add tool validations (completed)
3. ✅ Create documentation (completed)
4. ⏳ Monitor error rates in production

### Future Enhancements
1. **Add Unit Tests**: Test new validation logic
2. **Agent Training**: Use examples in agent context
3. **Metrics Dashboard**: Visualize file edit patterns
4. **Auto-Repair**: Detect and suggest corrections for common patterns
5. **Fuzzy Matching**: Offer suggestions when pattern is close but not exact

## References

- **Main Documentation**: `docs/reducing-file-edit-errors.md`
- **Tool Documentation**: `docs/tools/file_edit.md`
- **Agent Instructions**: 
  - `src/agents/vercel/debug.ts`
  - `src/agents/BuiltInAgents.ts`
- **Tool Implementation**: `src/tools/fileOperations.ts`

## Summary

This implementation addresses the root causes of frequent file editing errors through a multi-layered approach:

1. **Prevention**: Tool-level validation catches errors before they happen
2. **Guidance**: Enhanced error messages guide agents toward correct solutions
3. **Education**: Comprehensive agent instructions establish best practices
4. **Documentation**: Clear guidelines for developers and future improvements

The changes are backward-compatible and should significantly reduce destructive file overwrites while improving overall edit success rates.

